"""
调试搜索问题的脚本
"""

from src.db import get_db_session, Movie
from src.task import MovieIndexer
from src.embedding.embedding_client import get_embedding
from src.vector.milvus_store import get_milvus_store


def debug_database_content():
    """检查数据库中的爱情相关内容"""
    print("=== 检查数据库中的爱情相关内容 ===")
    
    with get_db_session() as session:
        # 检查genres字段包含"爱情"的电影
        love_movies_genres = session.query(Movie).filter(
            Movie.genres.like('%爱情%')
        ).limit(10).all()
        
        print(f"genres字段包含'爱情'的电影数量: {len(love_movies_genres)}")
        for movie in love_movies_genres[:5]:
            print(f"  - {movie.title} ({movie.year}) - 类型: {movie.genres}")
        
        # 检查description字段包含"爱情"的电影
        love_movies_desc = session.query(Movie).filter(
            Movie.description.like('%爱情%')
        ).limit(10).all()
        
        print(f"\ndescription字段包含'爱情'的电影数量: {len(love_movies_desc)}")
        for movie in love_movies_desc[:5]:
            print(f"  - {movie.title} ({movie.year}) - 描述: {movie.description[:100]}...")
        
        # 检查title字段包含"爱情"的电影
        love_movies_title = session.query(Movie).filter(
            Movie.title.like('%爱情%')
        ).limit(10).all()
        
        print(f"\ntitle字段包含'爱情'的电影数量: {len(love_movies_title)}")
        for movie in love_movies_title[:5]:
            print(f"  - {movie.title} ({movie.year})")


def debug_embedding_similarity():
    """测试embedding模型的相似度计算"""
    print("\n=== 测试embedding模型相似度 ===")
    
    try:
        embedding = get_embedding()
        
        # 测试词汇
        test_words = ["爱情", "英语", "动作", "科幻", "喜剧", "恐怖"]
        
        # 计算embeddings
        embeddings = {}
        for word in test_words:
            emb = embedding.embed_query(word)
            embeddings[word] = emb
            print(f"'{word}' embedding维度: {len(emb)}")
        
        # 计算相似度
        import numpy as np
        
        def cosine_similarity(a, b):
            return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))
        
        print(f"\n相似度矩阵:")
        query_word = "爱情"
        query_emb = embeddings[query_word]
        
        similarities = []
        for word in test_words:
            if word != query_word:
                sim = cosine_similarity(query_emb, embeddings[word])
                similarities.append((word, sim))
                print(f"'{query_word}' vs '{word}': {sim:.4f}")
        
        # 排序
        similarities.sort(key=lambda x: x[1], reverse=True)
        print(f"\n与'{query_word}'最相似的词:")
        for word, sim in similarities:
            print(f"  {word}: {sim:.4f}")
            
    except Exception as e:
        print(f"Embedding测试失败: {e}")


def debug_vector_search():
    """调试向量搜索"""
    print("\n=== 调试向量搜索 ===")
    
    try:
        indexer = MovieIndexer("test_movie_indexer_collection")
        
        # 检查集合是否存在
        if not indexer.client.has_collection(indexer.collection_name):
            print("向量集合不存在")
            return
        
        # 获取集合统计
        stats = indexer.get_collection_stats()
        print(f"集合统计: {stats}")
        
        # 测试向量搜索
        vector_store = get_milvus_store(indexer.collection_name)
        
        test_queries = ["爱情", "英语", "动作", "科幻"]
        
        for query in test_queries:
            print(f"\n搜索 '{query}':")
            results = vector_store.similarity_search(query, k=5)
            
            for i, doc in enumerate(results):
                movie_id = doc.metadata.get("movie_id")
                field = doc.metadata.get("field")
                content = doc.page_content[:50]
                print(f"  {i+1}. 字段'{field}': {content} (电影ID: {movie_id})")
                
                # 获取完整电影信息验证
                with get_db_session() as session:
                    movie = session.query(Movie).filter(Movie.id == movie_id).first()
                    if movie:
                        print(f"      电影: {movie.title} - 类型: {movie.genres}")
    
    except Exception as e:
        print(f"向量搜索调试失败: {e}")


def debug_document_generation():
    """调试文档生成过程"""
    print("\n=== 调试文档生成过程 ===")
    
    with get_db_session() as session:
        # 获取一些包含爱情的电影
        love_movies = session.query(Movie).filter(
            Movie.genres.like('%爱情%')
        ).limit(3).all()
        
        if not love_movies:
            print("没有找到包含'爱情'的电影")
            return
        
        indexer = MovieIndexer("test_movie_indexer_collection")
        
        for movie in love_movies:
            print(f"\n电影: {movie.title}")
            print(f"类型: {movie.genres}")
            print(f"描述: {movie.description[:100] if movie.description else 'N/A'}...")
            
            # 生成文档
            docs = indexer.movie_to_documents(movie)
            print(f"生成文档数量: {len(docs)}")
            
            for doc in docs:
                field = doc.metadata.get("field")
                content = doc.page_content[:50]
                print(f"  字段'{field}': {content}...")


if __name__ == "__main__":
    print("开始调试搜索问题...\n")
    
    # 1. 检查数据库内容
    debug_database_content()
    
    # 2. 测试embedding相似度
    debug_embedding_similarity()
    
    # 3. 调试向量搜索
    debug_vector_search()
    
    # 4. 调试文档生成
    debug_document_generation()
    
    print("\n调试完成！")
