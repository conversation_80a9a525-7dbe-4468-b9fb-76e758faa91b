"""
快速调试搜索问题
"""

from src.db import get_db_session, Movie
from src.embedding.embedding_client import get_embedding
import numpy as np


def quick_debug():
    print("=== 快速调试搜索问题 ===")
    
    # 1. 检查数据库中的爱情电影
    print("\n1. 检查数据库中的爱情相关电影:")
    with get_db_session() as session:
        # 检查genres包含爱情的电影
        love_movies = session.query(Movie).filter(
            Movie.genres.like('%爱情%')
        ).limit(5).all()
        
        print(f"找到 {len(love_movies)} 部爱情电影:")
        for movie in love_movies:
            print(f"  - {movie.title} | 类型: {movie.genres} | 年份: {movie.year}")
    
    # 2. 测试embedding相似度
    print("\n2. 测试embedding相似度:")
    try:
        embedding = get_embedding()
        
        # 测试词汇
        words = ["爱情", "英语", "动作", "科幻", "romance", "love"]
        
        print("计算embeddings...")
        embeddings = {}
        for word in words:
            emb = embedding.embed_query(word)
            embeddings[word] = np.array(emb)
            print(f"  '{word}' -> 维度: {len(emb)}")
        
        # 计算与"爱情"的相似度
        def cosine_similarity(a, b):
            return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))
        
        love_emb = embeddings["爱情"]
        print(f"\n与'爱情'的相似度:")
        for word in words:
            if word != "爱情":
                sim = cosine_similarity(love_emb, embeddings[word])
                print(f"  爱情 vs {word}: {sim:.4f}")
                
    except Exception as e:
        print(f"Embedding测试失败: {e}")
    
    # 3. 检查向量数据库中的数据
    print("\n3. 检查向量数据库中的数据:")
    try:
        from src.vector.milvus_store import get_milvus_store
        from src.task import MovieIndexer
        
        indexer = MovieIndexer("test_movie_indexer_collection")
        
        if not indexer.client.has_collection(indexer.collection_name):
            print("向量集合不存在，请先运行索引")
            return
        
        vector_store = get_milvus_store(indexer.collection_name)
        
        # 搜索不同的词
        test_queries = ["爱情", "romance", "love", "英语", "english"]
        
        for query in test_queries:
            print(f"\n搜索 '{query}':")
            results = vector_store.similarity_search(query, k=3)
            
            for i, doc in enumerate(results):
                field = doc.metadata.get("field")
                movie_id = doc.metadata.get("movie_id")
                content = doc.page_content[:30]
                print(f"  {i+1}. 字段'{field}': {content}... (电影ID: {movie_id})")
        
        # 4. 检查genres字段的文档
        print(f"\n4. 检查genres字段的文档:")
        results = vector_store.similarity_search("类型", k=10)
        genres_docs = [doc for doc in results if doc.metadata.get("field") == "genres"]
        
        print(f"找到 {len(genres_docs)} 个genres字段的文档:")
        for doc in genres_docs[:5]:
            content = doc.page_content
            movie_id = doc.metadata.get("movie_id")
            print(f"  电影ID {movie_id}: {content}")
            
    except Exception as e:
        print(f"向量数据库检查失败: {e}")


if __name__ == "__main__":
    quick_debug()
