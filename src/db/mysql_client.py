from contextlib import contextmanager
from typing import Generator

from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker

from src.config import appConfig

# 创建数据库引擎
engine = create_engine(
    appConfig.database.database_url,
    pool_size=appConfig.database.pool_size,
    max_overflow=appConfig.database.max_overflow,
    pool_timeout=appConfig.database.pool_timeout,
    pool_recycle=appConfig.database.pool_recycle,
    echo=appConfig.database.echo,
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_engine():
    """获取数据库引擎"""
    return engine


@contextmanager
def get_db_session() -> Generator[Session, None, None]:
    """获取数据库会话上下文管理器"""
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


def get_db_session_sync() -> Session:
    """获取数据库会话（同步版本，需要手动管理）"""
    return SessionLocal()
