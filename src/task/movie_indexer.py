from typing import List, Optional

from langchain_core.documents import Document
from loguru import logger
from pymilvus import CollectionSchema, DataType, FieldSchema
from sqlalchemy.orm import Session

from src.db import Movie, get_db_session
from src.vector.milvus_client import get_milvus_client
from src.vector.milvus_store import get_milvus_store


class MovieIndexer:
    """电影数据入库向量数据库服务"""

    def __init__(self, collection_name: str = "movies_collection"):
        self.collection_name = collection_name
        self.dim = 1024  # 向量维度，与 embedding 模型保持一致
        self.metric_type = "IP"  # 内积相似度
        self.client = get_milvus_client()

    def create_collection_if_not_exists(self):
        """创建向量集合（如果不存在）"""
        if not self.client.has_collection(self.collection_name):
            logger.info(f"创建向量集合: {self.collection_name}")
            
            # 定义 schema
            schema = CollectionSchema(
                fields=[
                    FieldSchema(
                        name="id", dtype=DataType.INT64, is_primary=True, auto_id=True
                    ),
                    FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=self.dim),
                    FieldSchema(name="text", dtype=DataType.VARCHAR, max_length=65535),
                    FieldSchema(name="movie_id", dtype=DataType.INT64),
                    FieldSchema(name="field", dtype=DataType.VARCHAR, max_length=50),
                    FieldSchema(name="year", dtype=DataType.INT64),
                    FieldSchema(name="score", dtype=DataType.FLOAT),
                ],
                description="电影数据向量集合",
                enable_dynamic_field=True,
            )

            # 创建集合
            self.client.create_collection(
                collection_name=self.collection_name,
                dimension=self.dim,
                metric_type=self.metric_type,
                schema=schema,
                auto_id=True,
            )
            logger.info(f"向量集合 {self.collection_name} 创建成功")
        else:
            logger.info(f"向量集合 {self.collection_name} 已存在")

    def drop_collection(self):
        """删除向量集合"""
        if self.client.has_collection(self.collection_name):
            self.client.drop_collection(self.collection_name)
            logger.info(f"向量集合 {self.collection_name} 已删除")

    def get_movies_from_db(
        self, 
        session: Session, 
        limit: Optional[int] = None,
        offset: int = 0
    ) -> List[Movie]:
        """从数据库获取电影数据"""
        query = session.query(Movie)
        
        if offset > 0:
            query = query.offset(offset)
        
        if limit:
            query = query.limit(limit)
            
        return query.all()

    def movie_to_documents(self, movie: Movie) -> List[Document]:
        """将电影数据转换为 Document 列表"""
        docs = []
        
        # 解析年份为整数
        year_int = None
        if movie.year:
            try:
                year_int = int(movie.year)
            except (ValueError, TypeError):
                year_int = None

        # 解析评分为浮点数
        score_float = None
        if movie.score:
            try:
                score_float = float(movie.score)
            except (ValueError, TypeError):
                score_float = None

        # 基础元数据
        base_metadata = {
            "movie_id": movie.id,
            "year": year_int,
            "score": score_float,
        }

        # 标题字段
        if movie.title:
            docs.append(
                Document(
                    page_content=movie.title,
                    metadata={**base_metadata, "field": "title"},
                )
            )

        # 外文标题
        if movie.foreign_title:
            docs.append(
                Document(
                    page_content=movie.foreign_title,
                    metadata={**base_metadata, "field": "foreign_title"},
                )
            )

        # 原始标题
        if movie.original_title:
            docs.append(
                Document(
                    page_content=movie.original_title,
                    metadata={**base_metadata, "field": "original_title"},
                )
            )

        # 演员字段
        if movie.actors:
            docs.append(
                Document(
                    page_content=movie.actors,
                    metadata={**base_metadata, "field": "actors"},
                )
            )

        # 导演字段
        if movie.directors:
            docs.append(
                Document(
                    page_content=movie.directors,
                    metadata={**base_metadata, "field": "directors"},
                )
            )

        # 编剧字段
        if movie.writers:
            docs.append(
                Document(
                    page_content=movie.writers,
                    metadata={**base_metadata, "field": "writers"},
                )
            )

        # 描述字段
        if movie.description:
            docs.append(
                Document(
                    page_content=movie.description,
                    metadata={**base_metadata, "field": "description"},
                )
            )

        # 类型字段
        if movie.genres:
            docs.append(
                Document(
                    page_content=movie.genres,
                    metadata={**base_metadata, "field": "genres"},
                )
            )

        # 国家字段
        if movie.countries:
            docs.append(
                Document(
                    page_content=movie.countries,
                    metadata={**base_metadata, "field": "countries"},
                )
            )

        # 语言字段
        if movie.languages:
            docs.append(
                Document(
                    page_content=movie.languages,
                    metadata={**base_metadata, "field": "languages"},
                )
            )

        return docs

    def index_movies(
        self, 
        limit: Optional[int] = None, 
        offset: int = 0,
        batch_size: int = 100
    ):
        """将电影数据索引到向量数据库"""
        logger.info(f"开始索引电影数据到向量集合: {self.collection_name}")
        
        # 创建集合
        self.create_collection_if_not_exists()
        
        # 获取向量存储
        vector_store = get_milvus_store(self.collection_name)
        
        with get_db_session() as session:
            # 获取电影数据
            movies = self.get_movies_from_db(session, limit, offset)
            logger.info(f"从数据库获取到 {len(movies)} 部电影")
            
            if not movies:
                logger.warning("没有找到电影数据")
                return
            
            # 批量处理
            total_docs = 0
            for i in range(0, len(movies), batch_size):
                batch_movies = movies[i:i + batch_size]
                batch_docs = []
                
                for movie in batch_movies:
                    docs = self.movie_to_documents(movie)
                    batch_docs.extend(docs)
                
                if batch_docs:
                    logger.info(f"正在索引第 {i//batch_size + 1} 批，包含 {len(batch_docs)} 个文档")
                    vector_store.add_documents(batch_docs)
                    total_docs += len(batch_docs)
            
            logger.info(f"总共索引了 {total_docs} 个文档")
            
            # flush & load
            logger.info("正在刷新和加载集合...")
            self.client.flush(collection_name=self.collection_name)
            self.client.load_collection(collection_name=self.collection_name)
            
            logger.info("电影数据索引完成")

    def get_collection_stats(self) -> dict:
        """获取集合统计信息"""
        if not self.client.has_collection(self.collection_name):
            return {"exists": False}
        
        stats = self.client.get_collection_stats(self.collection_name)
        return {
            "exists": True,
            "stats": stats,
        }
