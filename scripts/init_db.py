"""
数据库初始化脚本

用于创建数据库表和插入测试数据
"""

from datetime import date, datetime
from decimal import Decimal

from src.db import get_db_session
from src.db.models import Base, Movie
from src.db.mysql_client import get_engine


def create_tables():
    """创建数据库表"""
    print("正在创建数据库表...")
    Base.metadata.create_all(bind=get_engine())
    print("✅ 数据库表创建完成")


def insert_sample_data():
    """插入示例数据"""
    print("正在插入示例数据...")
    
    sample_movies = [
        Movie(
            id=1,
            douban_id="1292052",
            title="霸王别姬",
            foreign_title="Farewell My Concubine",
            original_title="霸王别姬",
            year="1993",
            directors="陈凯歌",
            writers="李碧华, 芦苇, 陈凯歌",
            actors="张国荣, 张丰毅, 巩俐",
            genres="剧情, 爱情, 同性",
            countries="中国大陆, 香港",
            languages="汉语普通话",
            release_date=date(1993, 1, 1),
            duration="171分钟",
            score=Decimal("9.6"),
            votes="1234567",
            description="京剧伶人程蝶衣和段小楼的半生故事，见证了一个时代的兴衰",
            cover_url="https://example.com/farewell_my_concubine.jpg",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        ),
        Movie(
            id=2,
            douban_id="1652587",
            title="阿凡达",
            foreign_title="Avatar",
            original_title="Avatar",
            year="2009",
            directors="詹姆斯·卡梅隆",
            writers="詹姆斯·卡梅隆",
            actors="萨姆·沃辛顿, 佐伊·索尔达娜, 西格妮·韦弗",
            genres="动作, 科幻, 冒险",
            countries="美国",
            languages="英语, 西班牙语",
            release_date=date(2009, 12, 18),
            duration="162分钟",
            score=Decimal("8.8"),
            votes="987654",
            description="在遥远的潘多拉星球，人类与纳美人之间的冲突",
            cover_url="https://example.com/avatar.jpg",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        ),
        Movie(
            id=3,
            douban_id="1292720",
            title="泰坦尼克号",
            foreign_title="Titanic",
            original_title="Titanic",
            year="1997",
            directors="詹姆斯·卡梅隆",
            writers="詹姆斯·卡梅隆",
            actors="莱昂纳多·迪卡普里奥, 凯特·温斯莱特",
            genres="剧情, 爱情, 灾难",
            countries="美国",
            languages="英语, 法语, 德语, 瑞典语, 意大利语, 俄语",
            release_date=date(1997, 12, 19),
            duration="194分钟",
            score=Decimal("9.4"),
            votes="1567890",
            description="1912年泰坦尼克号邮轮沉没事件中的爱情故事",
            cover_url="https://example.com/titanic.jpg",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        ),
        Movie(
            id=4,
            douban_id="1292213",
            title="大话西游之大圣娶亲",
            foreign_title="A Chinese Odyssey Part Two: Cinderella",
            original_title="大话西游之大圣娶亲",
            year="1995",
            directors="刘镇伟",
            writers="刘镇伟",
            actors="周星驰, 朱茵, 吴孟达, 蔡少芬",
            genres="喜剧, 爱情, 奇幻",
            countries="香港, 中国大陆",
            languages="粤语, 汉语普通话",
            release_date=date(1995, 2, 4),
            duration="95分钟",
            score=Decimal("9.2"),
            votes="1345678",
            description="至尊宝为了救白晶晶而穿越时空的爱情喜剧",
            cover_url="https://example.com/chinese_odyssey.jpg",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        ),
        Movie(
            id=5,
            douban_id="1889243",
            title="星际穿越",
            foreign_title="Interstellar",
            original_title="Interstellar",
            year="2014",
            directors="克里斯托弗·诺兰",
            writers="乔纳森·诺兰, 克里斯托弗·诺兰",
            actors="马修·麦康纳, 安妮·海瑟薇, 杰西卡·查斯坦",
            genres="剧情, 科幻, 冒险",
            countries="美国, 英国, 加拿大, 冰岛",
            languages="英语",
            release_date=date(2014, 11, 7),
            duration="169分钟",
            score=Decimal("9.3"),
            votes="1789012",
            description="在地球面临毁灭时，一群探险家穿越虫洞寻找新家园",
            cover_url="https://example.com/interstellar.jpg",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        ),
    ]
    
    with get_db_session() as session:
        for movie in sample_movies:
            # 检查是否已存在
            existing = session.query(Movie).filter(Movie.id == movie.id).first()
            if not existing:
                session.add(movie)
        
        session.commit()
    
    print(f"✅ 插入了 {len(sample_movies)} 部示例电影")


def main():
    """主函数"""
    print("=== 数据库初始化 ===")
    
    try:
        # 创建表
        create_tables()
        
        # 插入示例数据
        insert_sample_data()
        
        print("\n✅ 数据库初始化完成")
        print("\n可以运行以下命令测试电影索引:")
        print("python examples/movie_indexing_example.py")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")


if __name__ == "__main__":
    main()
