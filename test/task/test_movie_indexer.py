import unittest
from datetime import date, datetime
from decimal import Decimal

from src.db import Movie, get_db_session
from src.task import MovieIndexer


class TestMovieIndexer(unittest.TestCase):
    """测试电影索引服务"""

    def setUp(self):
        self.indexer = MovieIndexer("test_movie_indexer_collection")

        # 清理测试集合
        self.indexer.drop_collection()

    def tearDown(self):
        """清理测试数据"""
        # self.indexer.drop_collection()

    def test_mysql_to_vector_conversion(self):
        """测试将 MySQL 中的现有电影数据转换到向量数据库"""
        print("\n=== 测试 MySQL 到向量数据库的完整转换流程 ===")

        # 1. 检查数据库中是否有电影数据
        try:
            with get_db_session() as session:
                total_count = session.query(Movie).count()
                print(f"数据库中共有 {total_count} 部电影")

                if total_count == 0:
                    self.skipTest("数据库中没有电影数据，请先添加一些电影数据")

                # 获取前5部电影进行测试（避免处理时间过长）
                movies = session.query(Movie).limit(5).all()
                print(f"选择前 {len(movies)} 部电影进行测试")

                for movie in movies:
                    print(f"  - ID: {movie.id}, 标题: {movie.title}, 年份: {movie.year}")

        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            self.skipTest(f"数据库连接或操作失败: {e}")

        # 2. 创建向量集合
        try:
            self.indexer.create_collection_if_not_exists()
            print("✅ 向量集合创建成功")
        except Exception as e:
            print(f"❌ 向量集合创建失败: {e}")
            self.skipTest(f"向量数据库连接失败: {e}")

        # 3. 循环处理每部电影：查询 → 转换 → 向量化
        try:
            from src.vector.milvus_store import get_milvus_store
            vector_store = get_milvus_store(self.indexer.collection_name)

            total_docs = 0
            processed_movies = []

            with get_db_session() as session:
                # 分批处理电影数据
                movies = session.query(Movie).limit(5).all()

                for i, movie in enumerate(movies, 1):
                    print(f"\n处理第 {i} 部电影: {movie.title}")

                    # 转换为文档
                    docs = self.indexer.movie_to_documents(movie)
                    print(f"  转换为 {len(docs)} 个文档")

                    if docs:
                        # 向量化并添加到向量数据库
                        vector_store.add_documents(docs)
                        total_docs += len(docs)
                        processed_movies.append(movie)
                        print(f"  ✅ 成功添加到向量数据库")
                    else:
                        print(f"  ⚠️ 没有生成文档（可能数据为空）")

                print(f"\n总共处理了 {len(processed_movies)} 部电影，生成 {total_docs} 个文档")

                # flush & load
                print("正在刷新和加载向量集合...")
                self.indexer.client.flush(collection_name=self.indexer.collection_name)
                self.indexer.client.load_collection(collection_name=self.indexer.collection_name)
                print("✅ 向量集合刷新完成")

        except Exception as e:
            print(f"❌ 向量化处理失败: {e}")
            raise

        # 4. 验证向量搜索功能
        try:
            from src.vector.milvus_store import get_milvus_store
            vector_store = get_milvus_store(self.indexer.collection_name)

            # 使用处理过的电影数据进行搜索测试
            if processed_movies:
                # 基于实际数据构建测试查询
                test_queries = []

                # 使用第一部电影的标题作为查询
                if processed_movies[0].title:
                    test_queries.append(processed_movies[0].title)

                # 使用演员名字作为查询（如果有的话）
                if processed_movies[0].actors:
                    actors = processed_movies[0].actors.split(',')[0].strip()
                    if actors:
                        test_queries.append(actors)

                # 添加一些通用查询
                test_queries.extend(["电影", "爱情", "动作"])

                for query in test_queries:
                    results = vector_store.similarity_search(query, k=3)
                    print(f"\n搜索 '{query}' 找到 {len(results)} 个结果:")

                    for j, doc in enumerate(results):
                        movie_id = doc.metadata.get("movie_id")
                        field = doc.metadata.get("field")
                        year = doc.metadata.get("year")
                        score = doc.metadata.get("score")
                        content = doc.page_content[:50] + "..." if len(doc.page_content) > 50 else doc.page_content
                        print(f"  {j + 1}. {content}")
                        print(f"     电影ID: {movie_id}, 字段: {field}, 年份: {year}, 评分: {score}")

                print("✅ 向量搜索功能验证成功")
            else:
                print("⚠️ 没有处理任何电影，跳过搜索验证")

        except Exception as e:
            print(f"❌ 向量搜索验证失败: {e}")
            raise

        # 5. 显示集合统计信息
        try:
            stats = self.indexer.get_collection_stats()
            if stats["exists"]:
                print(f"\n📊 向量集合统计信息: {stats['stats']}")

        except Exception as e:
            print(f"⚠️ 获取统计信息失败: {e}")

        print("\n🎉 MySQL 到向量数据库转换测试完成！")
        print("💡 提示：这个测试使用了数据库中的真实数据，没有修改任何 MySQL 数据")


if __name__ == "__main__":
    unittest.main()
