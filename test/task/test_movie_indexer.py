import unittest
from datetime import date, datetime
from decimal import Decimal

from src.db import Movie, get_db_session
from src.task import MovieIndexer


class TestMovieIndexer(unittest.TestCase):
    """测试电影索引服务"""

    def setUp(self):
        self.indexer = MovieIndexer("test_movie_indexer_collection")

        # 清理测试集合
        self.indexer.drop_collection()

    def tearDown(self):
        """清理测试数据"""
        # self.indexer.drop_collection()

    def test_mysql_to_vector_conversion(self):
        """测试将 MySQL 中的现有电影数据转换到向量数据库"""
        print("\n=== 测试 MySQL 到向量数据库的完整转换流程 ===")

        # 1. 检查数据库中是否有电影数据
        try:
            with get_db_session() as session:
                total_count = session.query(Movie).count()
                print(f"数据库中共有 {total_count} 部电影")

                if total_count == 0:
                    self.skipTest("数据库中没有电影数据，请先添加一些电影数据")

                print(f"将分批处理所有 {total_count} 部电影，每批 1000 条")

        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            self.skipTest(f"数据库连接或操作失败: {e}")

        # 2. 创建向量集合
        try:
            self.indexer.create_collection_if_not_exists()
            print("✅ 向量集合创建成功")
        except Exception as e:
            print(f"❌ 向量集合创建失败: {e}")
            self.skipTest(f"向量数据库连接失败: {e}")

        # 3. 分批循环处理所有电影：查询 → 转换 → 向量化
        try:
            from src.vector.milvus_store import get_milvus_store
            vector_store = get_milvus_store(self.indexer.collection_name)

            batch_size = 1000  # 每批处理1000条
            total_docs = 0
            total_processed = 0
            processed_movies = []

            with get_db_session() as session:
                # 获取总数量
                total_count = session.query(Movie).count()
                total_batches = (total_count + batch_size - 1) // batch_size
                print(f"总共需要处理 {total_batches} 批")

                # 分批处理
                for batch_num in range(total_batches):
                    offset = batch_num * batch_size
                    print(f"\n=== 处理第 {batch_num + 1}/{total_batches} 批 (偏移量: {offset}) ===")

                    # 获取当前批次的电影数据
                    movies = session.query(Movie).offset(offset).limit(batch_size).all()
                    print(f"获取到 {len(movies)} 部电影")

                    if not movies:
                        print("没有更多数据，结束处理")
                        break

                    # 处理当前批次的每部电影
                    batch_docs = []
                    batch_processed = 0

                    for i, movie in enumerate(movies, 1):
                        if i % 100 == 0 or i == len(movies):
                            print(f"  处理进度: {i}/{len(movies)} ({i / len(movies) * 100:.1f}%)")

                        # 转换为文档
                        docs = self.indexer.movie_to_documents(movie)

                        if docs:
                            batch_docs.extend(docs)
                            processed_movies.append(movie)
                            batch_processed += 1

                    # 批量添加到向量数据库
                    if batch_docs:
                        print(f"  正在向量化 {len(batch_docs)} 个文档...")
                        vector_store.add_documents(batch_docs)
                        total_docs += len(batch_docs)
                        total_processed += batch_processed
                        print(f"  ✅ 成功处理 {batch_processed} 部电影，生成 {len(batch_docs)} 个文档")
                    else:
                        print(f"  ⚠️ 当前批次没有生成任何文档")

                    # 每批处理完后进行一次 flush
                    print(f"  正在刷新向量集合...")
                    self.indexer.client.flush(collection_name=self.indexer.collection_name)

                print(f"\n🎉 所有批次处理完成！")
                print(f"总共处理了 {total_processed} 部电影，生成 {total_docs} 个文档")

                # 最终 load 集合
                print("正在加载向量集合...")
                self.indexer.client.load_collection(collection_name=self.indexer.collection_name)
                print("✅ 向量集合加载完成")

        except Exception as e:
            print(f"❌ 向量化处理失败: {e}")
            raise

        # 4. 显示集合统计信息
        try:
            stats = self.indexer.get_collection_stats()
            if stats["exists"]:
                print(f"\n📊 向量集合统计信息: {stats['stats']}")

        except Exception as e:
            print(f"⚠️ 获取统计信息失败: {e}")

        print("\n🎉 MySQL 到向量数据库转换测试完成！")
        print("💡 提示：这个测试使用了数据库中的真实数据，没有修改任何 MySQL 数据")

    def test_search(self):
        """验证向量搜索功能"""
        from src.vector.milvus_store import get_milvus_store
        vector_store = get_milvus_store(self.indexer.collection_name)
        res = vector_store.similarity_search("科幻电影", k=10)
        print(res)


if __name__ == "__main__":
    unittest.main()
