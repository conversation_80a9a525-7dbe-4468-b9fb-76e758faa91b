import unittest
from datetime import date, datetime
from decimal import Decimal

from src.db import Movie, get_db_session
from src.task import MovieIndexer


class TestMovieIndexer(unittest.TestCase):
    """测试电影索引服务"""

    def setUp(self):
        self.indexer = MovieIndexer("test_movie_indexer_collection")
        
        # 清理测试集合
        self.indexer.drop_collection()

    def tearDown(self):
        """清理测试数据"""
        self.indexer.drop_collection()

    def test_create_collection(self):
        """测试创建集合"""
        self.indexer.create_collection_if_not_exists()
        
        # 验证集合已创建
        assert self.indexer.client.has_collection(self.indexer.collection_name)
        
        # 获取集合统计信息
        stats = self.indexer.get_collection_stats()
        assert stats["exists"] is True

    def test_movie_to_documents(self):
        """测试电影数据转换为文档"""
        # 创建测试电影数据
        movie = Movie(
            id=1,
            douban_id="1292052",
            title="霸王别姬",
            foreign_title="Farewell My Concubine",
            original_title="霸王别姬",
            year="1993",
            directors="陈凯歌",
            writers="李碧华, 芦苇, 陈凯歌",
            actors="张国荣, 张丰毅, 巩俐",
            genres="剧情, 爱情, 同性",
            countries="中国大陆, 香港",
            languages="汉语普通话",
            release_date=date(1993, 1, 1),
            duration="171分钟",
            score=Decimal("9.6"),
            votes="1234567",
            description="京剧伶人程蝶衣和段小楼的半生故事",
            cover_url="https://example.com/cover.jpg",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        
        # 转换为文档
        docs = self.indexer.movie_to_documents(movie)
        
        # 验证文档数量和内容
        assert len(docs) > 0
        
        # 验证每个文档都有正确的元数据
        for doc in docs:
            assert doc.metadata["movie_id"] == 1
            assert doc.metadata["year"] == 1993
            assert doc.metadata["score"] == 9.6
            assert "field" in doc.metadata
            assert doc.page_content is not None

    def test_index_movies_with_mock_data(self):
        """测试索引电影数据（使用模拟数据）"""
        # 注意：这个测试需要实际的数据库连接和数据
        # 在实际使用时，需要确保数据库中有测试数据

        # 创建集合
        self.indexer.create_collection_if_not_exists()

        # 尝试索引数据（限制数量避免测试时间过长）
        try:
            self.indexer.index_movies(limit=5)
            print("电影数据索引测试完成")
        except Exception as e:
            print(f"索引测试跳过（可能没有数据库连接或数据）: {e}")

    def test_mysql_to_vector_conversion(self):
        """测试将 MySQL 中的电影数据转换到向量数据库"""
        print("\n=== 测试 MySQL 到向量数据库的完整转换流程 ===")

        # 1. 首先在数据库中插入测试数据
        test_movies = [
            Movie(
                id=9001,
                douban_id="test_001",
                title="测试电影1",
                foreign_title="Test Movie 1",
                original_title="Test Movie 1",
                year="2023",
                directors="测试导演1",
                writers="测试编剧1",
                actors="测试演员1, 测试演员2",
                genres="测试类型",
                countries="测试国家",
                languages="测试语言",
                release_date=date(2023, 1, 1),
                duration="120分钟",
                score=Decimal("8.5"),
                votes="12345",
                description="这是一个测试电影的描述，用于验证向量化功能",
                cover_url="https://test.com/cover1.jpg",
                created_at=datetime.now(),
                updated_at=datetime.now(),
            ),
            Movie(
                id=9002,
                douban_id="test_002",
                title="测试电影2",
                foreign_title="Test Movie 2",
                original_title="Test Movie 2",
                year="2024",
                directors="测试导演2",
                writers="测试编剧2",
                actors="测试演员3, 测试演员4",
                genres="科幻, 动作",
                countries="测试国家2",
                languages="测试语言2",
                release_date=date(2024, 1, 1),
                duration="150分钟",
                score=Decimal("9.0"),
                votes="54321",
                description="这是第二个测试电影，包含科幻和动作元素",
                cover_url="https://test.com/cover2.jpg",
                created_at=datetime.now(),
                updated_at=datetime.now(),
            ),
        ]

        # 2. 插入测试数据到数据库
        try:
            with get_db_session() as session:
                # 清理可能存在的测试数据
                session.query(Movie).filter(Movie.id.in_([9001, 9002])).delete()
                session.commit()

                # 插入新的测试数据
                for movie in test_movies:
                    session.add(movie)
                session.commit()

                print(f"✅ 成功插入 {len(test_movies)} 部测试电影到数据库")

                # 验证数据已插入
                count = session.query(Movie).filter(Movie.id.in_([9001, 9002])).count()
                assert count == 2, f"期望插入2部电影，实际插入{count}部"

        except Exception as e:
            print(f"❌ 数据库操作失败: {e}")
            self.skipTest(f"数据库连接或操作失败: {e}")

        # 3. 创建向量集合
        try:
            self.indexer.create_collection_if_not_exists()
            print("✅ 向量集合创建成功")
        except Exception as e:
            print(f"❌ 向量集合创建失败: {e}")
            self.skipTest(f"向量数据库连接失败: {e}")

        # 4. 执行数据转换和索引
        try:
            # 只索引我们的测试数据
            with get_db_session() as session:
                test_movies_from_db = session.query(Movie).filter(
                    Movie.id.in_([9001, 9002])
                ).all()

                print(f"从数据库获取到 {len(test_movies_from_db)} 部测试电影")

                # 转换为文档
                all_docs = []
                for movie in test_movies_from_db:
                    docs = self.indexer.movie_to_documents(movie)
                    all_docs.extend(docs)
                    print(f"电影 '{movie.title}' 转换为 {len(docs)} 个文档")

                print(f"总共生成 {len(all_docs)} 个文档")

                # 添加到向量数据库
                from src.vector.milvus_store import get_milvus_store
                vector_store = get_milvus_store(self.indexer.collection_name)
                vector_store.add_documents(all_docs)

                # flush & load
                self.indexer.client.flush(collection_name=self.indexer.collection_name)
                self.indexer.client.load_collection(collection_name=self.indexer.collection_name)

                print("✅ 文档成功添加到向量数据库")

        except Exception as e:
            print(f"❌ 向量化处理失败: {e}")
            raise

        # 5. 验证向量搜索功能
        try:
            from src.vector.milvus_store import get_milvus_store
            vector_store = get_milvus_store(self.indexer.collection_name)

            # 测试搜索
            test_queries = ["测试电影", "科幻", "测试演员1"]

            for query in test_queries:
                results = vector_store.similarity_search(query, k=5)
                print(f"\n搜索 '{query}' 找到 {len(results)} 个结果:")

                for i, doc in enumerate(results):
                    movie_id = doc.metadata.get("movie_id")
                    field = doc.metadata.get("field")
                    print(f"  {i+1}. {doc.page_content} (电影ID: {movie_id}, 字段: {field})")

                # 验证至少找到一些结果
                assert len(results) > 0, f"搜索 '{query}' 应该返回结果"

            print("✅ 向量搜索功能验证成功")

        except Exception as e:
            print(f"❌ 向量搜索验证失败: {e}")
            raise

        # 6. 清理测试数据
        try:
            with get_db_session() as session:
                session.query(Movie).filter(Movie.id.in_([9001, 9002])).delete()
                session.commit()
                print("✅ 测试数据清理完成")
        except Exception as e:
            print(f"⚠️ 测试数据清理失败: {e}")

        print("\n🎉 MySQL 到向量数据库转换测试完成！")


if __name__ == "__main__":
    unittest.main()
