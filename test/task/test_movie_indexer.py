import unittest
from datetime import date, datetime
from decimal import Decimal

from src.db import Movie, get_db_session
from src.task import MovieIndexer


class TestMovieIndexer(unittest.TestCase):
    """测试电影索引服务"""

    def setUp(self):
        self.indexer = MovieIndexer("test_movie_indexer_collection")
        
        # 清理测试集合
        self.indexer.drop_collection()

    def tearDown(self):
        """清理测试数据"""
        self.indexer.drop_collection()

    def test_create_collection(self):
        """测试创建集合"""
        self.indexer.create_collection_if_not_exists()
        
        # 验证集合已创建
        assert self.indexer.client.has_collection(self.indexer.collection_name)
        
        # 获取集合统计信息
        stats = self.indexer.get_collection_stats()
        assert stats["exists"] is True

    def test_movie_to_documents(self):
        """测试电影数据转换为文档"""
        # 创建测试电影数据
        movie = Movie(
            id=1,
            douban_id="1292052",
            title="霸王别姬",
            foreign_title="Farewell My Concubine",
            original_title="霸王别姬",
            year="1993",
            directors="陈凯歌",
            writers="李碧华, 芦苇, 陈凯歌",
            actors="张国荣, 张丰毅, 巩俐",
            genres="剧情, 爱情, 同性",
            countries="中国大陆, 香港",
            languages="汉语普通话",
            release_date=date(1993, 1, 1),
            duration="171分钟",
            score=Decimal("9.6"),
            votes="1234567",
            description="京剧伶人程蝶衣和段小楼的半生故事",
            cover_url="https://example.com/cover.jpg",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        
        # 转换为文档
        docs = self.indexer.movie_to_documents(movie)
        
        # 验证文档数量和内容
        assert len(docs) > 0
        
        # 验证每个文档都有正确的元数据
        for doc in docs:
            assert doc.metadata["movie_id"] == 1
            assert doc.metadata["year"] == 1993
            assert doc.metadata["score"] == 9.6
            assert "field" in doc.metadata
            assert doc.page_content is not None

    def test_index_movies_with_mock_data(self):
        """测试索引电影数据（使用模拟数据）"""
        # 注意：这个测试需要实际的数据库连接和数据
        # 在实际使用时，需要确保数据库中有测试数据
        
        # 创建集合
        self.indexer.create_collection_if_not_exists()
        
        # 尝试索引数据（限制数量避免测试时间过长）
        try:
            self.indexer.index_movies(limit=5)
            print("电影数据索引测试完成")
        except Exception as e:
            print(f"索引测试跳过（可能没有数据库连接或数据）: {e}")


if __name__ == "__main__":
    unittest.main()
