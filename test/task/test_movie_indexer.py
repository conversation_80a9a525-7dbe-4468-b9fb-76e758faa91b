import unittest
from datetime import date, datetime
from decimal import Decimal

from src.db import Movie, get_db_session
from src.retriever.factory import init_retriever
from src.task import MovieIndexer


class TestMovieIndexer(unittest.TestCase):
    """测试电影索引服务"""

    def setUp(self):
        self.indexer = MovieIndexer("test_movie_indexer_collection")

    def tearDown(self):
        """清理测试数据"""
        # self.indexer.drop_collection()

    def test_mysql_to_vector_conversion(self):
        # 清理测试集合
        self.indexer.drop_collection()

        """测试将 MySQL 中的现有电影数据转换到向量数据库"""
        print("\n=== 测试 MySQL 到向量数据库的完整转换流程 ===")

        # 1. 检查数据库中是否有电影数据
        try:
            with get_db_session() as session:
                total_count = session.query(Movie).count()
                print(f"数据库中共有 {total_count} 部电影")

                if total_count == 0:
                    self.skipTest("数据库中没有电影数据，请先添加一些电影数据")

                print(f"将分批处理所有 {total_count} 部电影，每批 1000 条")

        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            self.skipTest(f"数据库连接或操作失败: {e}")

        # 2. 创建向量集合
        try:
            self.indexer.create_collection_if_not_exists()
            print("✅ 向量集合创建成功")
        except Exception as e:
            print(f"❌ 向量集合创建失败: {e}")
            self.skipTest(f"向量数据库连接失败: {e}")

        # 3. 分批循环处理所有电影：查询 → 转换 → 向量化
        try:
            from src.vector.milvus_store import get_milvus_store
            vector_store = get_milvus_store(self.indexer.collection_name)

            batch_size = 1000  # 每批处理1000条
            total_docs = 0
            total_processed = 0
            processed_movies = []

            with get_db_session() as session:
                # 获取总数量
                total_count = session.query(Movie).count()
                total_batches = (total_count + batch_size - 1) // batch_size
                print(f"总共需要处理 {total_batches} 批")

                # 分批处理
                for batch_num in range(total_batches):
                    offset = batch_num * batch_size
                    print(f"\n=== 处理第 {batch_num + 1}/{total_batches} 批 (偏移量: {offset}) ===")

                    # 获取当前批次的电影数据
                    movies = session.query(Movie).offset(offset).limit(batch_size).all()
                    print(f"获取到 {len(movies)} 部电影")

                    if not movies:
                        print("没有更多数据，结束处理")
                        break

                    # 处理当前批次的每部电影
                    batch_docs = []
                    batch_processed = 0
                    movies_with_no_docs = 0

                    for i, movie in enumerate(movies, 1):
                        if i % 100 == 0 or i == len(movies):
                            print(f"  处理进度: {i}/{len(movies)} ({i / len(movies) * 100:.1f}%)")

                        # 转换为文档
                        docs = self.indexer.movie_to_documents(movie)

                        if docs:
                            batch_docs.extend(docs)
                            processed_movies.append(movie)
                            batch_processed += 1
                        else:
                            movies_with_no_docs += 1
                            print(f"  ⚠️ 电影 ID {movie.id} 没有生成任何文档")

                    if movies_with_no_docs > 0:
                        print(f"  注意: {movies_with_no_docs} 部电影没有生成文档")

                    # 验证文档元数据的完整性
                    if batch_docs:
                        print(f"  正在验证 {len(batch_docs)} 个文档的元数据...")
                        valid_docs = []
                        for doc in batch_docs:
                            # 确保所有必需的元数据字段都存在且有效
                            if (doc.metadata.get("movie_id") is not None and
                                doc.metadata.get("year") is not None and
                                doc.metadata.get("score") is not None and
                                doc.metadata.get("field") is not None):
                                valid_docs.append(doc)
                            else:
                                print(f"  ⚠️ 发现无效文档元数据: {doc.metadata}")

                        if valid_docs:
                            print(f"  正在向量化 {len(valid_docs)} 个有效文档...")
                            vector_store.add_documents(valid_docs)
                            total_docs += len(valid_docs)
                            total_processed += batch_processed
                            print(f"  ✅ 成功处理 {batch_processed} 部电影，生成 {len(valid_docs)} 个有效文档")
                        else:
                            print(f"  ❌ 当前批次没有有效文档")
                    else:
                        print(f"  ⚠️ 当前批次没有生成任何文档")

                    # 每批处理完后进行一次 flush
                    print(f"  正在刷新向量集合...")
                    self.indexer.client.flush(collection_name=self.indexer.collection_name)

                print(f"\n🎉 所有批次处理完成！")
                print(f"总共处理了 {total_processed} 部电影，生成 {total_docs} 个文档")

                # 最终 load 集合
                print("正在加载向量集合...")
                self.indexer.client.load_collection(collection_name=self.indexer.collection_name)
                print("✅ 向量集合加载完成")

        except Exception as e:
            print(f"❌ 向量化处理失败: {e}")
            raise

        # 4. 显示集合统计信息
        try:
            stats = self.indexer.get_collection_stats()
            if stats["exists"]:
                print(f"\n📊 向量集合统计信息: {stats['stats']}")

        except Exception as e:
            print(f"⚠️ 获取统计信息失败: {e}")

        print("\n🎉 MySQL 到向量数据库转换测试完成！")
        print("💡 提示：这个测试使用了数据库中的真实数据，没有修改任何 MySQL 数据")

    def test_search(self):
        """验证向量搜索功能"""
        retriever = init_retriever(self.indexer.collection_name)
        res = retriever.invoke("啤酒节")
        print(res)

    def test_search_with_complete_movie_info(self):
        """测试搜索并获取完整的电影信息"""
        print("\n=== 测试完整电影信息搜索 ===")

        # 确保集合存在
        if not self.indexer.client.has_collection(self.indexer.collection_name):
            print("向量集合不存在，请先运行 test_mysql_to_vector_conversion")
            return

        test_queries = ["爱情", "动作", "科幻", "喜剧"]

        for query in test_queries:
            print(f"\n搜索关键词: '{query}'")

            # 方法1: 基础向量搜索（只返回文档片段）
            print("--- 基础搜索结果（文档片段）---")
            from src.vector.milvus_store import get_milvus_store
            vector_store = get_milvus_store(self.indexer.collection_name)
            basic_results = vector_store.similarity_search(query, k=3)

            for i, doc in enumerate(basic_results):
                movie_id = doc.metadata.get("movie_id")
                field = doc.metadata.get("field")
                content = doc.page_content[:50] + "..." if len(doc.page_content) > 50 else doc.page_content
                print(f"  {i+1}. 字段 '{field}': {content} (电影ID: {movie_id})")

            # 方法2: 完整电影信息搜索
            print("--- 完整电影信息 ---")
            try:
                movies_info = self.indexer.search_movies_with_details(query, k=5)

                for i, movie_info in enumerate(movies_info[:2]):  # 只显示前2部
                    movie = movie_info["movie"]
                    matched_fields = movie_info["matched_fields"]

                    print(f"\n  电影 {i+1}:")
                    print(f"    ID: {movie.get('id')}")
                    print(f"    标题: {movie.get('title', 'N/A')}")
                    print(f"    年份: {movie.get('year', 'N/A')}")
                    print(f"    评分: {movie.get('score', 'N/A')}")
                    print(f"    类型: {movie.get('genres', 'N/A')}")
                    print(f"    演员: {(movie.get('actors', 'N/A') or 'N/A')[:80]}...")
                    print(f"    描述: {(movie.get('description', 'N/A') or 'N/A')[:100]}...")
                    print(f"    匹配字段: {', '.join(matched_fields)}")

                    # 方法3: 根据movie_id获取完整信息
                    complete_movie = self.indexer.get_movie_by_id(movie.get('id'))
                    if complete_movie:
                        print(f"    完整信息验证: ✅ (从数据库获取)")
                    else:
                        print(f"    完整信息验证: ❌ (数据库中未找到)")

            except Exception as e:
                print(f"完整搜索失败: {e}")

            print("-" * 50)


if __name__ == "__main__":
    unittest.main()
