"""
电影数据索引示例

这个示例展示如何使用 MovieIndexer 将 MySQL 中的电影数据索引到向量数据库中。
"""

from src.task import MovieIndexer
from src.retriever.factory import init_retriever


def main():
    """主函数"""
    # 创建电影索引器
    indexer = MovieIndexer("movies_collection")
    
    print("=== 电影数据索引示例 ===")
    
    # 1. 索引电影数据
    print("\n1. 开始索引电影数据...")
    try:
        # 索引前100部电影（可以根据需要调整）
        indexer.index_movies(limit=100, batch_size=20)
        print("✅ 电影数据索引完成")
    except Exception as e:
        print(f"❌ 索引失败: {e}")
        return
    
    # 2. 获取集合统计信息
    print("\n2. 获取集合统计信息...")
    stats = indexer.get_collection_stats()
    if stats["exists"]:
        print(f"✅ 集合存在，统计信息: {stats['stats']}")
    else:
        print("❌ 集合不存在")
        return
    
    # 3. 测试检索功能
    print("\n3. 测试检索功能...")
    try:
        # 测试搜索
        test_queries = [
            "霸王别姬",
            "张国荣",
            "科幻电影",
            "爱情故事",
        ]

        for query in test_queries:
            print(f"\n=== 搜索: '{query}' ===")

            # 方法1: 基础文档片段搜索
            print("基础搜索结果（文档片段）:")
            retriever = init_retriever("movies_collection")
            results = retriever.invoke(query)

            for i, doc in enumerate(results[:3]):  # 只显示前3个结果
                movie_id = doc.metadata.get("movie_id")
                field = doc.metadata.get("field")
                year = doc.metadata.get("year")
                score = doc.metadata.get("score")
                content = doc.page_content[:50] + "..." if len(doc.page_content) > 50 else doc.page_content

                print(f"  {i+1}. 字段 '{field}': {content}")
                print(f"     电影ID: {movie_id}, 年份: {year}, 评分: {score}")

            # 方法2: 完整电影信息搜索
            print("\n完整电影信息:")
            movies_info = indexer.search_movies_with_details(query, k=3)

            for i, movie_info in enumerate(movies_info[:2]):  # 只显示前2部电影
                movie = movie_info["movie"]
                matched_fields = movie_info["matched_fields"]

                print(f"\n  电影 {i+1}:")
                print(f"    标题: {movie.get('title', 'N/A')}")
                print(f"    年份: {movie.get('year', 'N/A')}")
                print(f"    评分: {movie.get('score', 'N/A')}")
                print(f"    类型: {movie.get('genres', 'N/A')}")
                print(f"    演员: {(movie.get('actors', '') or 'N/A')[:80]}...")
                print(f"    描述: {(movie.get('description', '') or 'N/A')[:100]}...")
                print(f"    匹配字段: {', '.join(matched_fields)}")

    except Exception as e:
        print(f"❌ 检索测试失败: {e}")
    
    print("\n=== 示例完成 ===")


if __name__ == "__main__":
    main()
