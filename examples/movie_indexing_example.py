"""
电影数据索引示例

这个示例展示如何使用 MovieIndexer 将 MySQL 中的电影数据索引到向量数据库中。
"""

from src.task import MovieIndexer
from src.retriever.factory import init_retriever


def main():
    """主函数"""
    # 创建电影索引器
    indexer = MovieIndexer("movies_collection")
    
    print("=== 电影数据索引示例 ===")
    
    # 1. 索引电影数据
    print("\n1. 开始索引电影数据...")
    try:
        # 索引前100部电影（可以根据需要调整）
        indexer.index_movies(limit=100, batch_size=20)
        print("✅ 电影数据索引完成")
    except Exception as e:
        print(f"❌ 索引失败: {e}")
        return
    
    # 2. 获取集合统计信息
    print("\n2. 获取集合统计信息...")
    stats = indexer.get_collection_stats()
    if stats["exists"]:
        print(f"✅ 集合存在，统计信息: {stats['stats']}")
    else:
        print("❌ 集合不存在")
        return
    
    # 3. 测试检索功能
    print("\n3. 测试检索功能...")
    try:
        # 初始化检索器
        retriever = init_retriever("movies_collection")
        
        # 测试搜索
        test_queries = [
            "霸王别姬",
            "张国荣",
            "科幻电影",
            "爱情故事",
        ]
        
        for query in test_queries:
            print(f"\n搜索: '{query}'")
            results = retriever.invoke(query)
            print(f"找到 {len(results)} 个结果:")
            
            for i, doc in enumerate(results[:3]):  # 只显示前3个结果
                movie_id = doc.metadata.get("movie_id")
                field = doc.metadata.get("field")
                year = doc.metadata.get("year")
                score = doc.metadata.get("score")
                
                print(f"  {i+1}. {doc.page_content[:50]}...")
                print(f"     电影ID: {movie_id}, 字段: {field}, 年份: {year}, 评分: {score}")
    
    except Exception as e:
        print(f"❌ 检索测试失败: {e}")
    
    print("\n=== 示例完成 ===")


if __name__ == "__main__":
    main()
